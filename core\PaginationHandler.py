import asyncio
from playwright.async_api import async_playwright, <PERSON>, B<PERSON>er, BrowserContext
from typing import Optional, Dict, Any, Union, Callable, Awaitable
import logging
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger('PaginationHandler')

class PaginationHandler:
    async def _detect_pagination_availability(self, next_button_selector, timeout=10000):
        """
        检测页面是否有有效的翻页功能（补充缺失方法）
        :param next_button_selector: 下一页按钮选择器
        :param timeout: 检测超时时间
        :return: True表示有翻页功能，False表示没有
        """
        try:
            logger.info(f"🔍 检测翻页功能，选择器: {next_button_selector}")
            # 检查是否存在下一页按钮
            next_buttons = await self.page.query_selector_all(next_button_selector)
            if not next_buttons:
                logger.info("❌ 未找到下一页按钮")
                return False
            logger.info(f"✅ 找到 {len(next_buttons)} 个匹配的元素")
            # 检查按钮是否可见和可用
            for i, button in enumerate(next_buttons):
                try:
                    is_visible = await button.is_visible()
                    is_enabled = await button.is_enabled()
                    text = await button.text_content()
                    href = await button.get_attribute('href')
                    onclick = await button.get_attribute('onclick')
                    logger.info(f"  按钮 {i+1}: 可见={is_visible}, 可用={is_enabled}, 文本='{text}', href='{href}', onclick='{onclick}'")
                    if is_visible and is_enabled:
                        return True
                except Exception as e:
                    logger.warning(f"  检查按钮 {i+1} 时出错: {e}")
            logger.info("❌ 没有找到可用的翻页按钮")
            return False
        except Exception as e:
            logger.error(f"翻页功能检测失败: {e}")
            return False
    """翻页处理器，支持多种动态翻页方式"""

    def __init__(self, page: Page):
        self.page = page
        self.all_articles = []  # 存储所有收集到的文章

    async def debug_pagination_elements(self, selectors: list = None) -> dict:
        """
        调试翻页元素，检查页面上可能的翻页按钮
        :param selectors: 要检查的选择器列表，如果为None则使用默认列表
        :return: 调试信息字典
        """
        if selectors is None:
            selectors = [
                "a.next:not(.lose)",
                "a.page-link:has-text('Next')",
                "a[onclick*='page']",
                ".js_pageI:not(.cur)",
                ".pager a",
                "a:contains('下一页')",
                "a:contains('Next')",
                ".next-page"
            ]

        debug_info = {
            'found_elements': [],
            'page_url': self.page.url,
            'page_title': await self.page.title()
        }

        for selector in selectors:
            try:
                elements = await self.page.query_selector_all(selector)
                if elements:
                    for i, element in enumerate(elements):
                        text = await element.text_content()
                        is_visible = await element.is_visible()
                        is_enabled = await element.is_enabled()
                        debug_info['found_elements'].append({
                            'selector': selector,
                            'index': i,
                            'text': text.strip() if text else '',
                            'visible': is_visible,
                            'enabled': is_enabled
                        })
            except Exception as e:
                logger.warning(f"Error checking selector {selector}: {str(e)}")

        return debug_info
    
    async def click_pagination(
        self,
        next_button_selector: str,
        max_pages: int = 10,
        content_ready_selector: Optional[str] = None,
        timeout: int = 10000,
        wait_after_click: int = 1000,
        disabled_check: bool = True,
        extract_articles_config: dict = None,
        stop_on_duplicate_last_item: bool = True,  # 新增：遇到重复item时提前结束
        auto_detect_pagination: bool = True,  # 新增：自动检测翻页功能
        next_selectors: list = None,  # 新增：可选的下一页选择器列表
        use_simple_pagination: bool = False  # 新增：使用简单翻页模式（参考test_tjszx_pagination.py）
    ) -> int:
        """
        点击翻页处理并提取文章
        :param next_button_selector: 下一页按钮的CSS选择器
        :param max_pages: 最大翻页次数
        :param content_ready_selector: 内容加载完成的标识选择器
        :param timeout: 超时时间(毫秒)
        :param wait_after_click: 点击后等待的时间（毫秒）
        :param disabled_check: 是否检查按钮禁用状态
        :param extract_articles_config: 文章提取配置字典
        :param stop_on_duplicate_last_item: 遇到重复item时提前结束
        :param auto_detect_pagination: 自动检测翻页功能
        :return: 实际翻页次数
        """
        current_page = 1
        total_articles_extracted = 0
        last_page_last_item_url = None  # 新增：记录上一页最后一个item的url

        # 如果启用简单翻页模式，使用类似test_tjszx_pagination.py的逻辑
        if use_simple_pagination:
            logger.info("🚀 使用简单翻页模式（参考test_tjszx_pagination.py）")
            return await self._simple_click_pagination(
                next_button_selector=next_button_selector,
                max_pages=max_pages,
                wait_after_click=wait_after_click,
                extract_articles_config=extract_articles_config
            )

        # 自动检测翻页功能
        if auto_detect_pagination:
            pagination_available = await self._detect_pagination_availability(next_button_selector, timeout)
            if not pagination_available:
                logger.warning("⚠️ 未检测到有效的翻页功能")
                logger.info("📋 可能的原因:")
                logger.info("   1. 这是一个单页面，没有翻页功能")
                logger.info("   2. 翻页按钮选择器不正确")
                logger.info("   3. 页面使用了其他翻页机制（如无限滚动）")
                logger.info("💡 建议:")
                logger.info("   - 检查页面是否真的有翻页功能")
                logger.info("   - 尝试使用传统翻页模式")
                logger.info("   - 或者使用滚动翻页模式")

                # 仍然提取第一页内容，然后返回
                if extract_articles_config:
                    try:
                        articles_count = await self.extract_articles_from_page(**extract_articles_config)
                        total_articles_extracted += articles_count
                        logger.info(f"✅ 已提取第1页的 {articles_count} 篇文章")
                    except Exception as e:
                        logger.warning(f"第1页文章提取失败: {e}")

                logger.info("🔄 由于没有翻页功能，爬取结束")
                return current_page

        # 首先提取第一页的文章
        if extract_articles_config:
            try:
                articles_count = await self.extract_articles_from_page(**extract_articles_config)
                total_articles_extracted += articles_count
                logger.info(f"第1页提取到 {articles_count} 篇文章")
                # 新增：记录第一页最后一个item的url
                if stop_on_duplicate_last_item and self.all_articles:
                    last_page_last_item_url = self.all_articles[-1][1] if len(self.all_articles[-1]) > 1 else None
            except Exception as e:
                logger.warning(f"第1页文章提取失败: {e}")

        # 开始翻页循环
        logger.info(f"开始翻页循环，当前页={current_page}, 最大页数={max_pages}")
        if not next_selectors:
            next_selectors = [
                'a:has-text("下一页")',
                'a:has-text("下页")',
                'a:has-text("下一頁")',
                'a.next',
                'a.next-page',
                '.pagination .next',
                'a[title="下一页"]',
                'button:has-text("下一页")',
                next_button_selector  # 保持兼容
            ]

        while current_page < max_pages:
            logger.info(f"尝试翻到第 {current_page + 1} 页...")

            # 等待内容加载
            if content_ready_selector:
                try:
                    await self.page.wait_for_selector(
                        content_ready_selector,
                        state="attached",
                        timeout=timeout
                    )
                    logger.info(f"Page {current_page} content loaded")
                except Exception as e:
                    logger.warning(f"Content loading warning: {str(e)}")

            # 记录翻页前的 URL
            prev_url = self.page.url

            # 每次循环都重新查找 next 按钮，遍历所有 next_selectors
            next_button = None
            next_selector_used = None
            for selector in next_selectors:
                try:
                    btns = await self.page.query_selector_all(selector)
                    for btn in btns:
                        is_visible = await btn.is_visible()
                        if not is_visible:
                            continue
                        is_disabled = await btn.evaluate('(el) => el.hasAttribute("disabled") || el.classList.contains("disabled") || el.classList.contains("unactive") || el.classList.contains("lose")')
                        if is_disabled:
                            logger.info(f"下一页按钮被禁用: {selector}")
                            continue
                        next_button = btn
                        next_selector_used = selector
                        break
                    if next_button:
                        break
                except Exception as e:
                    logger.debug(f"查找下一页按钮 {selector} 出错: {e}")
                    continue

            if not next_button:
                logger.error("❌ 未找到可用的下一页按钮，所有选择器均尝试失败")
                break

            logger.info(f"✅ 找到可用的下一页按钮，选择器: {next_selector_used}")

            # 获取按钮信息用于调试
            button_text = await next_button.text_content()
            button_href = await next_button.get_attribute('href')
            logger.info(f"下一页按钮信息: 文本='{button_text}', href='{button_href}'")


            # 点击下一页按钮
            logger.info("点击下一页按钮...")
            await next_button.click()
            # 等待页面资源加载完毕（参考 test_tjszx_pagination.py）
            try:
                await self.page.wait_for_load_state('networkidle', timeout=timeout)
                logger.info("页面网络空闲，资源加载完毕")
            except Exception as e:
                logger.warning(f"等待 networkidle 超时: {e}")
            # 再额外等待一段时间，确保内容渲染
            await self.page.wait_for_timeout(wait_after_click)
            current_page += 1
            logger.info(f"✅ 成功点击，当前页面: {current_page}")

            # 等待“下一页”按钮消失再出现，或内容区变化（保留，兼容部分站点）
            try:
                await self.page.wait_for_selector(next_selector_used, state="detached", timeout=timeout)
                await self.page.wait_for_selector(next_selector_used, state="attached", timeout=timeout)
                logger.info("下一页按钮已刷新，页面加载完成")
            except Exception as e:
                logger.warning(f"等待下一页按钮刷新时出错: {str(e)}")

            # 翻页后对比 URL 辅助判断
            new_url = self.page.url
            if new_url == prev_url:
                logger.warning("翻页后 URL 未变化，可能翻页失败或为异步加载")

            # 提取当前页的文章
            if extract_articles_config:
                try:
                    logger.info(f"开始提取第{current_page}页的文章...")
                    before_count = len(self.all_articles)
                    articles_count = await self.extract_articles_from_page(**extract_articles_config)
                    total_articles_extracted += articles_count
                    logger.info(f"第{current_page}页提取到 {articles_count} 篇文章")

                    # 新增：判断最后item是否重复
                    if stop_on_duplicate_last_item and self.all_articles:
                        new_last_item_url = self.all_articles[-1][1] if len(self.all_articles[-1]) > 1 else None
                        if last_page_last_item_url and new_last_item_url == last_page_last_item_url:
                            logger.info("检测到最后一项网址与上一页相同，提前结束翻页。")
                            break
                        last_page_last_item_url = new_last_item_url
                except Exception as e:
                    logger.warning(f"第{current_page}页文章提取失败: {e}")

        logger.info(f"翻页完成！总共处理 {current_page} 页，提取 {total_articles_extracted} 篇文章")
        return current_page

    async def _simple_click_pagination(
        self,
        next_button_selector: str,
        max_pages: int,
        wait_after_click: int,
        extract_articles_config: dict = None
    ) -> int:
        """
        简单翻页模式，参考test_tjszx_pagination.py的成功逻辑
        """
        logger.info("🔄 开始简单翻页模式")

        # 定义多个下一页选择器，按优先级排序
        next_selectors = [
            'a:has-text("下一页")',
            'a:has-text("下页")',
            'a:has-text("下一頁")',
            'a.next',
            'a.next-page',
            '.pagination .next',
            'a[title="下一页"]',
            'button:has-text("下一页")',
            next_button_selector  # 用户指定的选择器
        ]

        # 首先提取第一页的文章
        if extract_articles_config:
            try:
                articles_count = await self.extract_articles_from_page(**extract_articles_config)
                logger.info(f"第1页提取到 {articles_count} 篇文章")
            except Exception as e:
                logger.warning(f"第1页文章提取失败: {e}")

        # 开始翻页循环
        current_page = 1
        click_count = 0

        while click_count < max_pages - 1:  # -1 因为第一页已经处理了
            logger.info(f"\n=== 尝试翻页到第 {current_page + 1} 页 ===")

            # 查找当前可用的下一页按钮
            next_btn = None
            used_selector = None
            for selector in next_selectors:
                try:
                    btn = await self.page.query_selector(selector)
                    if btn:
                        is_disabled = await btn.evaluate('(el) => el.hasAttribute("disabled") || el.classList.contains("disabled")')
                        is_visible = await btn.is_visible()
                        if not is_disabled and is_visible:
                            next_btn = btn
                            used_selector = selector
                            logger.info(f"✅ 找到可用的下一页按钮: {selector}")
                            break
                except Exception as e:
                    logger.debug(f"检查选择器 {selector} 时出错: {e}")
                    continue

            if not next_btn:
                logger.info("❌ 未找到可用的下一页按钮，翻页结束")
                break

            # 记录翻页前的URL
            initial_url = self.page.url
            logger.info(f"翻页前URL: {initial_url}")

            # 点击下一页按钮
            logger.info("🖱️ 点击下一页按钮...")
            await next_btn.click()

            # 等待页面加载完成（参考test_tjszx_pagination.py的成功逻辑）
            try:
                await self.page.wait_for_load_state('networkidle')
                logger.info("✅ 页面网络空闲，资源加载完毕")
            except Exception as e:
                logger.warning(f"等待networkidle超时: {e}")

            # 额外等待时间确保内容渲染
            await self.page.wait_for_timeout(wait_after_click)

            # 检查URL变化
            new_url = self.page.url
            logger.info(f"翻页后URL: {new_url}")

            if initial_url == new_url:
                logger.warning("⚠️ URL未变化，可能是AJAX翻页或已到最后一页")
            else:
                logger.info("✅ URL已变化，翻页成功")

            current_page += 1
            click_count += 1

            # 提取当前页的文章
            if extract_articles_config:
                try:
                    logger.info(f"📄 开始提取第{current_page}页的文章...")
                    articles_count = await self.extract_articles_from_page(**extract_articles_config)
                    logger.info(f"✅ 第{current_page}页提取到 {articles_count} 篇文章")
                except Exception as e:
                    logger.warning(f"❌ 第{current_page}页文章提取失败: {e}")

        logger.info(f"🎉 简单翻页完成！总共处理了 {current_page} 页")
        return current_page

    async def _detect_pagination_availability(self, next_button_selector, timeout=10000):
        """
        检测页面是否有有效的翻页功能
        :param next_button_selector: 下一页按钮选择器
        :param timeout: 检测超时时间
        :return: True表示有翻页功能，False表示没有
        """
        try:
            logger.info(f"🔍 检测翻页功能，选择器: {next_button_selector}")

            # 1. 检查是否存在下一页按钮
            next_buttons = await self.page.query_selector_all(next_button_selector)
            if not next_buttons:
                logger.info("❌ 未找到下一页按钮")
                return False

            logger.info(f"✅ 找到 {len(next_buttons)} 个匹配的元素")

            # 2. 检查按钮是否可见和可用
            visible_buttons = []
            for i, button in enumerate(next_buttons):
                try:
                    is_visible = await button.is_visible()
                    is_enabled = await button.is_enabled()
                    text = await button.text_content()
                    href = await button.get_attribute('href')
                    onclick = await button.get_attribute('onclick')

                    logger.info(f"  按钮 {i+1}: 可见={is_visible}, 可用={is_enabled}, 文本='{text}', href='{href}', onclick='{onclick}'")

                    if is_visible and is_enabled:
                        # 进一步检查按钮是否有实际功能
                        if href and href != 'javascript:;' and href != '#':
                            visible_buttons.append(button)
                            logger.info(f"    ✅ 按钮 {i+1} 有有效的href链接")
                        elif onclick and onclick.strip():
                            visible_buttons.append(button)
                            logger.info(f"    ✅ 按钮 {i+1} 有onclick事件")
                        elif text and any(keyword in text.lower() for keyword in ['下一页', 'next', '>', '更多']):
                            visible_buttons.append(button)
                            logger.info(f"    ✅ 按钮 {i+1} 有翻页相关文本")
                        else:
                            logger.info(f"    ⚠️ 按钮 {i+1} 可能没有实际功能")
                    else:
                        logger.info(f"    ❌ 按钮 {i+1} 不可见或不可用")

                except Exception as e:
                    logger.warning(f"  检查按钮 {i+1} 时出错: {e}")

            if not visible_buttons:
                logger.info("❌ 没有找到可用的翻页按钮")
                return False

            # 3. 尝试检测页面是否有多页内容的迹象
            pagination_indicators = await self._check_pagination_indicators()
            if not pagination_indicators:
                logger.info("⚠️ 未检测到多页内容的迹象")
                # 但仍然返回True，因为有可用的按钮

            logger.info(f"✅ 检测到有效的翻页功能，找到 {len(visible_buttons)} 个可用按钮")
            return True

        except Exception as e:
            logger.error(f"翻页功能检测失败: {e}")
            return False

    async def _check_pagination_indicators(self) -> bool:
        """
        检查页面是否有多页内容的迹象
        :return: True表示有多页迹象，False表示可能是单页
        """
        try:
            # 检查常见的分页指示器
            pagination_selectors = [
                ".pagination",
                ".page-nav",
                ".page-list",
                ".pager",
                "a[href*='page']",
                "a[href*='index_']",
                ".page-number",
                ".page-item"
            ]

            for selector in pagination_selectors:
                elements = await self.page.query_selector_all(selector)
                if elements:
                    logger.info(f"  ✅ 找到分页指示器: {selector} ({len(elements)} 个)")
                    return True

            # 检查是否有数字链接（页码）
            all_links = await self.page.query_selector_all("a")
            digit_links = 0
            for link in all_links[:20]:  # 只检查前20个链接
                try:
                    text = await link.text_content()
                    if text and text.strip().isdigit():
                        digit_links += 1
                except:
                    continue

            if digit_links >= 2:  # 如果有2个或更多数字链接，可能是页码
                logger.info(f"  ✅ 找到 {digit_links} 个数字链接，可能是页码")
                return True

            logger.info("  ❌ 未找到明显的分页指示器")
            return False

        except Exception as e:
            logger.warning(f"分页指示器检查失败: {e}")
            return False

    async def _find_alternative_pagination_buttons(self) -> list:
        """
        查找替代的翻页按钮
        :return: 可见的翻页按钮列表
        """
        try:
            # 常见的翻页按钮选择器
            alternative_selectors = [
                # 链接类型的翻页按钮
                "a:has-text('下一页')",
                "a:has-text('next')",
                "a:has-text('Next')",
                "a:has-text('>')",
                "a:has-text('>>')",
                "a:has-text('更多')",
                "a[title*='下一页']",
                "a[title*='next']",
                "a[href*='page']",
                "a[href*='index_']",
                "a[onclick*='page']",
                "a[onclick*='Page']",
                ".pagination a:last-child",
                ".page-nav a:last-child",
                ".pager a:last-child",

                # input按钮类型的翻页按钮（常见于政府网站）
                "input[value='下一页']",
                "input[value='next']",
                "input[value='Next']",
                "input[value='>']",
                "input[value='>>']",
                "input[type='button'][value*='下一页']",
                "input[type='submit'][value*='下一页']",
                "input[onclick*='page']",
                "input[onclick*='Page']",
                "input[onclick*='next']",

                # 按钮类型
                "button:has-text('下一页')",
                "button:has-text('next')",
                "button:has-text('Next')",
                "button:has-text('>')",
                "button[onclick*='page']",
                "button[onclick*='next']"
            ]

            found_buttons = []

            for selector in alternative_selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    for element in elements:
                        is_visible = await element.is_visible()
                        is_enabled = await element.is_enabled()

                        if is_visible and is_enabled:
                            # 进一步验证这是一个有效的翻页按钮
                            text = await element.text_content()
                            href = await element.get_attribute('href')
                            onclick = await element.get_attribute('onclick')

                            # 检查是否有实际的翻页功能
                            tag_name = await element.evaluate("el => el.tagName.toLowerCase()")

                            # 对于input元素，检查value属性
                            if tag_name == 'input':
                                value = await element.get_attribute('value')
                                input_type = await element.get_attribute('type')
                                if value and any(keyword in value.lower() for keyword in ['下一页', 'next', '>', '更多']):
                                    found_buttons.append(element)
                                    logger.info(f"  ✅ 找到替代按钮: 选择器={selector}, input类型={input_type}, value='{value}'")
                                    continue

                            # 对于其他元素，检查常规属性
                            if (href and href not in ['javascript:;', '#', '']) or \
                               (onclick and onclick.strip()) or \
                               (text and any(keyword in text.lower() for keyword in ['下一页', 'next', '>', '更多'])):
                                found_buttons.append(element)
                                logger.info(f"  ✅ 找到替代按钮: 选择器={selector}, 文本='{text}', href='{href}'")

                except Exception as e:
                    logger.debug(f"检查选择器 {selector} 时出错: {e}")
                    continue

            return found_buttons

        except Exception as e:
            logger.error(f"查找替代翻页按钮失败: {e}")
            return []

    async def _detect_pagination_mode(
        self,
        scroll_container_selector: str = "body",
        load_element_pattern: Optional[str] = None
    ) -> str:
        """
        智能检测网页的翻页模式
        :param scroll_container_selector: 滚动容器选择器
        :param load_element_pattern: 加载元素模式
        :return: "load_elements" 或 "traditional"
        """
        # 1. 如果明确指定了加载元素模式，优先使用
        if load_element_pattern and '{n}' in load_element_pattern:
            logger.info(f"检测到指定的加载元素模式: {load_element_pattern}")
            return "load_elements"

        # 2. 自动检测常见的加载元素模式
        common_load_patterns = [
            "#load{n}",
            "#loadMore{n}",
            ".load{n}",
            ".load-more-{n}",
            "[id^='load'][id$='{n}']"
        ]

        for pattern in common_load_patterns:
            # 检测 #load1, #load2 等元素
            base_pattern = pattern.replace('{n}', '1')
            try:
                element = await self.page.query_selector(base_pattern)
                if element:
                    logger.info(f"自动检测到加载元素模式: {pattern}")
                    return "load_elements"
            except Exception:
                continue

        # 3. 检测当前URL是否包含已知的加载元素网站特征
        current_url = self.page.url
        load_element_sites = [
            "shrd.gov.cn",  # 上海人大网站
            "npc.gov.cn",   # 全国人大网站
            # 可以添加更多已知使用加载元素的网站
        ]

        for site in load_element_sites:
            if site in current_url:
                logger.info(f"检测到已知的加载元素网站: {site}")
                # 为这些网站设置默认的加载元素模式
                return "load_elements"

        # 4. 默认使用传统滚动模式
        logger.info("使用传统滚动模式")
        return "traditional"

    async def scroll_pagination(
        self,
        scroll_container_selector: str = "body",
        scroll_step: int = 1000,
        scroll_delay: int = 1000,
        max_scrolls: int = 20,
        load_indicator_selector: Optional[str] = None,
        scroll_timeout: int = 10000,
        height_tolerance: int = 50,
        load_element_pattern: Optional[str] = None,
        max_loads: int = 50,
        extract_articles_config: dict = None
    ) -> int:
        """
        统一的滚动翻页处理函数
        支持两种模式：
        1. 传统滚动模式：基于高度变化检测
        2. 加载元素模式：基于 #load{n} 等动态元素检测

        :param scroll_container_selector: 滚动容器的CSS选择器
        :param scroll_step: 每次滚动像素数
        :param scroll_delay: 滚动间隔(毫秒)
        :param max_scrolls: 最大滚动次数（传统模式）
        :param load_indicator_selector: 加载指示器的选择器（传统模式）
        :param scroll_timeout: 滚动加载超时时间
        :param height_tolerance: 高度变化容忍值（传统模式）
        :param load_element_pattern: 加载元素模式，如 "#load{n}"（加载元素模式）
        :param max_loads: 最大加载次数（加载元素模式）
        :param extract_articles_config: 文章提取配置（加载元素模式）
        :return: 实际滚动/加载次数
        """
        logger.info("开始智能滚动翻页...")

        # 智能检测翻页模式
        detected_mode = await self._detect_pagination_mode(
            scroll_container_selector=scroll_container_selector,
            load_element_pattern=load_element_pattern
        )

        # 根据检测结果选择模式
        if detected_mode == "load_elements":
            # 如果没有指定load_element_pattern，使用默认的#load{n}
            if not load_element_pattern or '{n}' not in load_element_pattern:
                load_element_pattern = "#load{n}"
                logger.info(f"使用默认加载元素模式: {load_element_pattern}")

            return await self._scroll_with_load_elements(
                load_element_pattern=load_element_pattern,
                max_loads=max_loads,
                scroll_delay=scroll_delay,
                scroll_step=scroll_step,
                extract_articles_config=extract_articles_config
            )
        else:
            # 使用传统滚动模式
            return await self._scroll_traditional(
                scroll_container_selector=scroll_container_selector,
                scroll_step=scroll_step,
                scroll_delay=scroll_delay,
                max_scrolls=max_scrolls,
                load_indicator_selector=load_indicator_selector,
                scroll_timeout=scroll_timeout,
                height_tolerance=height_tolerance
            )

    async def _scroll_traditional(
        self,
        scroll_container_selector: str,
        scroll_step: int,
        scroll_delay: int,
        max_scrolls: int,
        load_indicator_selector: Optional[str],
        scroll_timeout: int,
        height_tolerance: int
    ) -> int:
        """
        传统滚动模式：基于高度变化检测
        """
        logger.info(f"使用传统滚动模式，容器: {scroll_container_selector}")
        scroll_count = 0
        last_height = 0

        # 获取滚动容器
        try:
            container = await self.page.wait_for_selector(
                scroll_container_selector,
                state="attached",
                timeout=scroll_timeout
            )

            is_hidden = await container.is_hidden()
            if is_hidden:
                logger.warning(f"滚动容器 {scroll_container_selector} 被隐藏，尝试显示")
                await container.scroll_into_view_if_needed()
                await self.page.wait_for_timeout(1000)

        except Exception as e:
            logger.error(f"滚动容器未找到: {str(e)}")

            # 尝试备用选择器
            backup_selectors = ["body", "html", ".content", "#content", ".main"]
            for backup_selector in backup_selectors:
                try:
                    logger.info(f"尝试备用选择器: {backup_selector}")
                    container = await self.page.wait_for_selector(backup_selector, timeout=2000)
                    logger.info(f"成功找到备用容器: {backup_selector}")
                    break
                except:
                    continue
            else:
                logger.error("未找到合适的滚动容器")
                return 0

        while scroll_count < max_scrolls:
            # 执行滚动
            await container.evaluate(f"container => container.scrollBy(0, {scroll_step})")
            await self.page.wait_for_timeout(scroll_delay)

            # 获取当前滚动高度
            new_height = await container.evaluate("container => container.scrollHeight")

            # 检测是否滚动到底部
            if abs(new_height - last_height) <= height_tolerance:
                logger.info("没有更多内容可加载（滚动高度未变化）")
                break

            last_height = new_height
            scroll_count += 1
            logger.info(f"已滚动 {scroll_count} 次，高度: {new_height}px")

            # 等待加载完成
            if load_indicator_selector:
                # 检查是否是无效的 {n} 模式选择器
                if '{n}' in load_indicator_selector:
                    logger.warning(f"检测到无效的加载指示器选择器: {load_indicator_selector}")
                    logger.warning("这应该使用加载元素模式，而不是传统滚动模式")
                else:
                    try:
                        await self.page.wait_for_selector(
                            load_indicator_selector,
                            state="visible",
                            timeout=2000
                        )
                        await self.page.wait_for_selector(
                            load_indicator_selector,
                            state="hidden",
                            timeout=10000
                        )
                        logger.info("加载指示器处理完成")
                    except Exception as e:
                        logger.warning(f"加载指示器错误: {str(e)}")

        return scroll_count

    async def _scroll_with_load_elements(
        self,
        load_element_pattern: str,
        max_loads: int,
        scroll_delay: int,
        scroll_step: int,
        extract_articles_config: dict = None
    ) -> int:
        """
        加载元素模式：基于 #load{n} 等动态元素检测
        """
        logger.info(f"使用加载元素模式: {load_element_pattern}")
        logger.info(f"最大加载次数: {max_loads}, 滚动延迟: {scroll_delay}ms")

        load_count = 0
        total_articles_extracted = 0

        # 首先提取第一页的文章
        if extract_articles_config:
            try:
                articles_count = await self.extract_articles_from_page(**extract_articles_config)
                total_articles_extracted += articles_count
                logger.info(f"初始页面提取到 {articles_count} 篇文章")
            except Exception as e:
                logger.warning(f"初始页面文章提取失败: {e}")

        # 开始滚动加载循环
        consecutive_failures = 0
        max_consecutive_failures = 1

        # 添加重复检测
        last_urls_count = 0
        consecutive_no_new_urls = 0
        max_consecutive_no_new = 3  # 连续3次没有新URL则停止

        for i in range(1, max_loads + 1):
            try:
                # 构造当前加载元素的选择器
                current_load_selector = load_element_pattern.replace("{n}", str(i))
                logger.info(f"第{i}次加载，查找元素: {current_load_selector}")

                # 持续滚动直到找到加载元素或超时
                element_found = False
                scroll_attempts = 0
                max_scroll_attempts = 10

                while not element_found and scroll_attempts < max_scroll_attempts:
                    logger.info(f"第{scroll_attempts + 1}次滚动，寻找元素: {current_load_selector}")
                    await self.page.evaluate(f"window.scrollBy(0, {scroll_step})")
                    await self.page.wait_for_timeout(scroll_delay)

                    # 检查加载元素是否出现
                    try:
                        load_element = await self.page.wait_for_selector(
                            current_load_selector,
                            state="attached",
                            timeout=3000
                        )

                        if load_element:
                            logger.info(f"✅ 找到加载元素 {current_load_selector}")
                            element_found = True
                            load_count += 1
                            consecutive_failures = 0

                            # 等待加载元素完全加载完成
                            logger.info("等待加载元素完成加载...")
                            await self.page.wait_for_timeout(2000)

                            # 提取新加载的文章
                            if extract_articles_config:
                                try:
                                    before_count = len(self.all_articles)
                                    articles_count = await self.extract_articles_from_page(**extract_articles_config)
                                    after_count = len(self.all_articles)
                                    new_articles = after_count - before_count
                                    total_articles_extracted += new_articles
                                    logger.info(f"第{i}次加载提取到 {new_articles} 篇新文章")
                                except Exception as e:
                                    logger.warning(f"第{i}次加载文章提取失败: {e}")

                            # 继续滚动以触发下一次加载
                            logger.info("继续滚动以触发下一次加载...")
                            await self.page.evaluate(f"window.scrollBy(0, {scroll_step})")
                            await self.page.wait_for_timeout(1000)

                        else:
                            logger.info(f"❌ 未找到加载元素 {current_load_selector}")
                            break

                    except Exception as e:
                        logger.debug(f"第{scroll_attempts + 1}次滚动未找到元素: {current_load_selector}")
                        scroll_attempts += 1

                        if scroll_attempts >= max_scroll_attempts:
                            logger.info(f"❌ 经过{max_scroll_attempts}次滚动仍未找到 {current_load_selector}")
                            logger.info("可能已到达最后一页或加载完成")
                            consecutive_failures += 1
                            break

                # 如果没有找到元素，增加连续失败计数
                if not element_found:
                    consecutive_failures += 1
                    logger.info(f"连续失败次数: {consecutive_failures}/{max_consecutive_failures}")

                    if consecutive_failures >= max_consecutive_failures:
                        logger.info("连续失败次数过多，停止加载")
                        break

                # 检查是否有新URL
                current_urls_count = len(set([article[1] for article in self.all_articles if len(article) > 1]))
                if current_urls_count == last_urls_count:
                    consecutive_no_new_urls += 1
                    logger.info(f"连续 {consecutive_no_new_urls} 次没有新URL")
                    if consecutive_no_new_urls >= max_consecutive_no_new:
                        logger.info("连续多次没有新URL，停止滚动")
                        break
                else:
                    consecutive_no_new_urls = 0
                
                last_urls_count = current_urls_count

            except Exception as e:
                logger.error(f"第{i}次滚动加载出错: {str(e)}")
                consecutive_failures += 1
                if consecutive_failures >= max_consecutive_failures:
                    break

        logger.info(f"加载元素模式完成！总共加载 {load_count} 次，提取 {total_articles_extracted} 篇文章")
        return load_count



    async def handle_iframe_pagination(
        self,
        iframe_selector: str,
        pagination_type: str,
        **kwargs
    ) -> int:
        """
        处理iframe内的翻页
        :param iframe_selector: iframe的CSS选择器
        :param pagination_type: 翻页类型 ('click' 或 'scroll')
        :param kwargs: 传递给翻页方法的参数
        :return: 实际翻页/滚动次数
        """
        try:
            # 定位iframe
            frame_element = await self.page.wait_for_selector(iframe_selector)
            frame = await frame_element.content_frame()
            
            if not frame:
                raise ValueError(f"Could not get frame from selector: {iframe_selector}")
            
            # 创建iframe内的页面处理器
            iframe_handler = PaginationHandler(frame)
            
            if pagination_type == 'click':
                return await iframe_handler.click_pagination(**kwargs)
            elif pagination_type == 'scroll':
                return await iframe_handler.scroll_pagination(**kwargs)
            else:
                raise ValueError("Unsupported pagination type. Use 'click' or 'scroll'")
        except Exception as e:
            logger.error(f"Iframe pagination error: {str(e)}")
            return 0

    def add_articles(self, articles_list):
        """
        添加文章到all_articles列表

        Args:
            articles_list: 文章列表，格式为 [(title, href, save_dir, page_title, page_url, classid), ...]
        """
        if articles_list:
            self.all_articles.extend(articles_list)
            logger.info(f"添加了 {len(articles_list)} 篇文章，总计 {len(self.all_articles)} 篇")

    def get_all_articles(self):
        """
        获取所有收集到的文章

        Returns:
            list: 所有文章的列表
        """
        return self.all_articles.copy()

    def clear_articles(self):
        """清空文章列表"""
        self.all_articles.clear()
        logger.info("已清空文章列表")

    def get_articles_count(self):
        """获取文章总数"""
        return len(self.all_articles)

    async def extract_articles_from_page(self,
                                       list_container_selector=".main",
                                       article_item_selector=".clearfix.ty_list li a",
                                       title_selector=None,
                                       list_container_type="CSS",
                                       article_item_type="CSS",
                                       title_selector_type="CSS",
                                       save_dir="articles",
                                       page_title="动态翻页结果",
                                       classid="",
                                       base_url=None,
                                       url_mode="relative"):
        """
        从当前页面提取文章信息并添加到all_articles
        参考core.crawler.py的get_article_links和get_full_link函数逻辑

        Args:
            list_container_selector: 列表容器选择器
            article_item_selector: 文章项选择器
            title_selector: 标题选择器（可选）
            list_container_type: 列表容器选择器类型 (CSS/XPath)
            article_item_type: 文章项选择器类型 (CSS/XPath)
            title_selector_type: 标题选择器类型 (CSS/XPath)
            save_dir: 保存目录
            page_title: 页面标题
            classid: 分类ID
            base_url: 基础URL，用于构建完整链接
            url_mode: URL模式 ('absolute'/'relative')

        Returns:
            int: 本页提取到的文章数量
        """
        try:
            # 等待页面加载
            await self.page.wait_for_load_state('networkidle', timeout=10000)

            current_url = self.page.url
            if not base_url:
                base_url = current_url

            # 查找列表容器 - 支持多个容器（参考core.crawler.py的get_article_links）
            if list_container_type.upper() == "XPATH":
                containers = await self.page.query_selector_all(f"xpath={list_container_selector}")
            else:
                containers = await self.page.query_selector_all(list_container_selector)

            if not containers:
                logger.warning(f"未找到列表容器: {list_container_selector}")
                return 0

            current_page_articles = []
            total_articles_found = 0

            # 遍历所有容器（参考core.crawler.py逻辑）
            for container in containers:
                try:
                    # 查找文章项
                    if article_item_type.upper() == "XPATH":
                        articles = await container.query_selector_all(f"xpath={article_item_selector}")
                    else:
                        articles = await container.query_selector_all(article_item_selector)

                    if not articles:
                        continue

                    total_articles_found += len(articles)

                    # 处理每个文章项（参考core.crawler.py的get_article_links逻辑）
                    for article in articles:
                        try:
                            # 增强的链接提取逻辑（参考core.crawler.py）
                            href = await article.get_attribute('href')

                            # 若没有href，尝试查找子元素的a标签
                            if not href:
                                try:
                                    a_tag = await article.query_selector('a')
                                    if a_tag:
                                        href = await a_tag.get_attribute('href')
                                except Exception:
                                    pass

                            # 如果还是没有，尝试从onclick提取（参考core.crawler.py逻辑）
                            if not href:
                                onclick = await article.get_attribute('onclick')
                                if onclick:
                                    # 使用正则表达式提取URL（与core.crawler.py相同的模式）
                                    import re
                                    patterns = [
                                        r"location(?:\.href)?\s*=\s*['\"]([^'\"]+)",  # location='url'
                                        r"window\.open\s*\(\s*['\"]([^'\"]+)",       # window.open('url')
                                        r"window\.location\.href\s*=\s*['\"]([^'\"]+)",  # window.location.href='url'
                                        r"redirectTo\s*\(\s*['\"]([^'\"]+)"           # 自定义函数如 redirectTo('url')
                                    ]

                                    for pattern in patterns:
                                        match = re.search(pattern, onclick)
                                        if match:
                                            href = match.group(1)
                                            break

                            # 增强的标题提取逻辑（参考core.crawler.py）
                            title = ""
                            if title_selector:
                                try:
                                    if title_selector_type.upper() == "XPATH":
                                        title_element = await article.query_selector(f"xpath={title_selector}")
                                    else:
                                        title_element = await article.query_selector(title_selector)
                                    if title_element:
                                        title = await title_element.text_content()
                                except Exception:
                                    pass

                            # 如果没有专门的标题选择器或提取失败，使用文章元素的文本
                            if not title:
                                title = await article.text_content()

                            if title:
                                title = title.strip()

                            # 处理完整URL（参考core.crawler.py的get_full_link逻辑）
                            if href:
                                full_url = self._get_full_link(href, current_url, base_url, url_mode)

                                # 构建文章信息元组，格式与core.crawler.py兼容
                                article_info = (title, full_url, save_dir, page_title, current_url, classid)
                                current_page_articles.append(article_info)

                        except Exception as e:
                            logger.warning(f"提取单个文章信息时出错: {e}")
                            continue

                except Exception as e:
                    logger.warning(f"处理容器时出错: {e}")
                    continue

            # 添加到总列表
            if current_page_articles:
                self.add_articles(current_page_articles)
                logger.info(f"从当前页面提取到 {len(current_page_articles)} 篇文章")

            # 添加URL去重逻辑
            unique_articles = []
            seen_urls = set()
            
            for article in self.all_articles:
                url = article[1] if len(article) > 1 else None
                if url and url not in seen_urls:
                    seen_urls.add(url)
                    unique_articles.append(article)
            
            # 更新文章列表为去重后的列表
            self.all_articles = unique_articles
            
            return len(current_page_articles)

        except Exception as e:
            logger.error(f"从页面提取文章时出错: {e}")
            return 0

    def _get_full_link(self, href, input_url, base_url, url_mode):
        """
        获取完整的链接（参考core.crawler.py的get_full_link函数）

        Args:
            href: 原始链接
            input_url: 输入URL
            base_url: 基础URL
            url_mode: URL模式 ('absolute'/'relative')

        Returns:
            str: 完整的URL
        """
        if not href:
            return ''

        # 绝对URL
        if href.startswith(('http://', 'https://')):
            return href

        # 协议相对URL
        if href.startswith('//'):
            scheme = base_url.split(':')[0] if ':' in base_url else 'https'
            return f"{scheme}:{href}"

        # 锚点
        if href.startswith('#'):
            from urllib.parse import urljoin
            return urljoin(base_url, href)

        # 相对路径处理（与core.crawler.py逻辑一致）
        from urllib.parse import urljoin
        if url_mode == "absolute":
            return urljoin(input_url, href)
        elif url_mode == "relative":
            return urljoin(base_url, href)
        else:
            return urljoin(base_url, href)

# ==================== 浏览器启动函数 ====================

async def launch_browser(
    p,  # 新增参数
    headless: bool = False,
    browser_type: str = "chromium",
    slow_mo: int = 100,
    proxy: Optional[Dict] = None,
    viewport: Dict[str, int] = {"width": 1280, "height": 720},
    user_agent: Optional[str] = None,
    **launch_kwargs
) -> tuple[Browser, BrowserContext, Page]:
    """
    启动Playwright浏览器
    :param p: async_playwright 实例
    :param headless: 是否无头模式
    :param browser_type: 浏览器类型 (chromium, firefox, webkit)
    :param slow_mo: 操作延迟(毫秒)
    :param proxy: 代理设置 {'server': 'http://host:port'}
    :param viewport: 视口大小 {'width': 1280, 'height': 720}
    :param user_agent: 自定义User-Agent
    :return: (browser, context, page)
    """
    # 选择浏览器类型
    browser_launcher = getattr(p, browser_type).launch
    
    # 准备启动参数
    launch_options = {
        "headless": headless,
        "slow_mo": slow_mo,
        **launch_kwargs
    }
    
    # 添加代理设置
    if proxy:
        launch_options["proxy"] = proxy
    
    browser = await browser_launcher(**launch_options)
    
    # 创建上下文
    context_options = {"viewport": viewport}
    if user_agent:
        context_options["user_agent"] = user_agent
    
    context = await browser.new_context(**context_options)
    page = await context.new_page()
    
    return browser, context, page

# ==================== 测试函数 ====================

async def click_pagination(p, mode='balance', config_file='config.json'):
    """测试点击分页功能
    Args:
        p: async_playwright 实例
        mode: 采集模式 (fast/safe/balance)
        config_file: 配置文件路径
    """
    browser, context, page = await launch_browser(p, headless=False)
    handler = PaginationHandler(page)
    
    # 使用测试网站（实际使用时替换为真实网站）
    await page.goto(
        "http://www.gysrd.gov.cn/so/search.shtml?tenantId=31&tenantIds=&configTenantId=&searchWord=%E7%90%86%E8%AE%BA%E7%A0%94%E7%A9%B6&dataTypeId=4775&sign=",
        timeout=60000,
        wait_until="domcontentloaded"
    )
    
    # 执行翻页
    pages_processed = await handler.click_pagination(
        next_button_selector="a.next:not(.lose)",
        content_ready_selector="",
        max_pages=5,
        wait_after_click=1500
    )
    
    logger.info(f"Processed {pages_processed} pages")
    
    # 截图保存结果
    await page.screenshot(path="click_pagination_result.png")
    await context.close()
    await browser.close()
    return pages_processed

async def scroll_pagination(p):
    """测试滚动翻页功能"""
    logger.info("Starting scroll pagination test...")
    browser, context, page = await launch_browser(p, headless=False)
    handler = PaginationHandler(page)
    
    # 使用测试网站（实际使用时替换为真实网站）
    await page.goto("https://www.shrd.gov.cn/n8347/n8403/index.html")
    
    # 执行滚动翻页
    scrolls_processed = await handler.scroll_pagination(
        scroll_container_selector="#largeData",
        scroll_step=800,
        scroll_delay=1500,
        max_scrolls=10,
        load_indicator_selector="#load{n}"
    )
    
    logger.info(f"Performed {scrolls_processed} scrolls")
    
    # 截图保存结果
    await page.screenshot(path="scroll_pagination_result.png")
    await context.close()
    await browser.close()
    return scrolls_processed

async def iframe_pagination(p):
    """测试iframe内翻页功能"""
    logger.info("Starting iframe pagination test...")
    browser, context, page = await launch_browser(p, headless=False)
    handler = PaginationHandler(page)

    # 使用包含iframe的测试页面，加载一个实际有分页的页面
    await page.set_content("""
    <html>
        <body>
            <h1>Main Page</h1>
            <iframe id="content-frame" src="http://www.gysrd.gov.cn/so/search.shtml?tenantId=31&tenantIds=&configTenantId=&searchWord=%E7%90%86%E8%AE%BA%E7%A0%94%E7%A9%B6&dataTypeId=4775&sign="></iframe>
        </body>
    </html>
    """)

    # 等待iframe加载
    try:
        await page.wait_for_selector("#content-frame", timeout=10000)
        await page.wait_for_timeout(3000)  # 等待iframe内容加载

        # 在iframe内执行翻页
        pages_processed = await handler.handle_iframe_pagination(
            iframe_selector="#content-frame",
            pagination_type="click",
            next_button_selector="a.next:not(.lose)",  # 使用与主测试相同的选择器
            max_pages=2  # 减少测试页数
        )

        logger.info(f"Processed {pages_processed} pages in iframe")
    except Exception as e:
        logger.warning(f"Iframe pagination test failed: {str(e)}")
        logger.info("This is expected if the iframe content doesn't load properly in test environment")
        pages_processed = 0

    # 截图保存结果
    await page.screenshot(path="iframe_pagination_result.png")
    await context.close()
    await browser.close()
    return pages_processed

async def main():
    import argparse
    parser = argparse.ArgumentParser(description='分页处理器测试参数')
    parser.add_argument('--mode', choices=['fast', 'safe', 'balance'], default='balance',
                        help='采集模式选择 (default: balance)')
    parser.add_argument('--config', type=str, default='config.json',
                        help='配置文件路径 (default: config.json)')
    parser.add_argument('--test', choices=['click', 'scroll', 'iframe', 'all'], default='all',
                        help='选择要运行的测试 (default: all)')
    args = parser.parse_args()

    results = {}

    async with async_playwright() as p:
        if args.test in ['click', 'all']:
            try:
                results['click_pagination'] = await click_pagination(p, mode=args.mode, config_file=args.config)
            except Exception as e:
                logger.error(f"Click pagination test failed: {str(e)}")
                results['click_pagination'] = 0

        if args.test in ['scroll', 'all']:
            try:
                results['scroll_pagination'] = await scroll_pagination(p)
            except Exception as e:
                logger.error(f"Scroll pagination test failed: {str(e)}")
                results['scroll_pagination'] = 0

        if args.test in ['iframe', 'all']:
            try:
                results['iframe_pagination'] = await iframe_pagination(p)
            except Exception as e:
                logger.error(f"Iframe pagination test failed: {str(e)}")
                results['iframe_pagination'] = 0

    
    # 打印测试结果
    logger.info("\n===== Test Results =====")
    for test_name, result in results.items():
        logger.info(f"{test_name}: {result}")

    # 如果所有测试都失败，提供帮助信息
    if all(result == 0 for result in results.values()):
        logger.info("\n===== 故障排除建议 =====")
        logger.info("所有测试都失败了，可能的原因:")
        logger.info("1. 网络连接问题")
        logger.info("2. 目标网站结构发生变化")
        logger.info("3. 分页选择器不正确")
        logger.info("建议使用 debug_pagination.py 脚本进行详细调试")

if __name__ == "__main__":
    asyncio.run(main())
